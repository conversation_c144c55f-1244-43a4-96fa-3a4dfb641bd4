# LCD屏幕不亮问题诊断

## 可能的原因和解决方案

### 1. 硬件连接问题
- **检查电源**：确保LCD模块有正确的电源供应（通常是3.3V或5V）
- **检查连接线**：确保所有数据线、控制线连接正确且接触良好
- **检查背光电源**：LCD背光通常需要单独的电源供应

### 2. 背光控制问题
根据代码分析，背光控制引脚是 **PB15**：
```c
#define LCD_BL_GPIO_PORT    GPIOB
#define LCD_BL_GPIO_PIN     SYS_GPIO_PIN15
#define LCD_BL(x)           sys_gpio_pin_set(LCD_BL_GPIO_PORT, LCD_BL_GPIO_PIN, x)
```

**解决方案**：
1. 检查PB15引脚是否正确配置为输出模式
2. 用万用表测量PB15引脚电压，应该是高电平（3.3V）
3. 检查背光电路是否需要额外的驱动电路

### 3. LCD初始化问题
代码中LCD初始化流程：
```c
lcd_init();           // LCD初始化
lcd_clear(WHITE);     // 清屏为白色
LCD_BL(1);           // 开启背光
```

**可能的问题**：
- LCD ID检测失败
- 初始化序列不正确
- 时序参数不匹配

### 4. 调试步骤

#### 步骤1：检查LCD ID
在串口输出中查找LCD ID信息。代码中有：
```c
// printf("LCD ID:%x\r\n", lcddev.id); /* 已注释掉 */
```

**建议**：临时取消注释这行代码来查看LCD ID是否正确检测。

#### 步骤2：强制背光开启
在main函数中添加强制背光控制：
```c
// 在lcd_init()之后添加
LCD_BL(1);              // 强制开启背光
delay_ms(100);
lcd_display_on();       // 强制开启显示
```

#### 步骤3：简化测试
创建一个最简单的LCD测试：
```c
void simple_lcd_test(void)
{
    // 强制配置背光引脚
    RCC->AHB1ENR |= 1 << 1;  // 使能GPIOB时钟
    
    GPIOB->MODER &= ~(3 << (15 * 2));
    GPIOB->MODER |= 1 << (15 * 2);     // PB15设为输出模式
    
    GPIOB->OTYPER &= ~(1 << 15);       // 推挽输出
    GPIOB->OSPEEDR |= 3 << (15 * 2);   // 高速
    GPIOB->PUPDR &= ~(3 << (15 * 2));  // 无上下拉
    
    // 强制输出高电平
    GPIOB->BSRR = 1 << 15;
    
    delay_ms(100);
    
    // 尝试简单的显示测试
    lcd_clear(RED);
    delay_ms(1000);
    lcd_clear(GREEN);
    delay_ms(1000);
    lcd_clear(BLUE);
}
```

### 5. 常见LCD型号的特殊要求

根据代码支持的LCD型号：
- **ILI9341**: 最常见，通常问题较少
- **ST7789**: 需要特殊的初始化序列
- **NT35510**: 需要密钥解锁
- **SSD1963**: 需要特殊的背光PWM控制

### 6. 电源和时序问题

**检查项目**：
- 系统时钟是否正确配置（168MHz）
- FSMC时序是否匹配LCD要求
- 电源纹波是否过大

### 7. 紧急解决方案

如果以上方法都不行，可以尝试：

1. **重新上电**：完全断电重启
2. **检查硬件**：用示波器检查信号
3. **替换LCD**：如果有备用LCD模块
4. **回退代码**：使用之前能正常工作的版本

### 8. 代码修改建议

在main函数的LCD初始化部分添加调试代码：

```c
// 在lcd_init()之前
printf("Starting LCD initialization...\r\n");

lcd_init();

// 在lcd_init()之后
printf("LCD ID: 0x%04X\r\n", lcddev.id);
printf("LCD Width: %d, Height: %d\r\n", lcddev.width, lcddev.height);

// 强制背光和显示
LCD_BL(1);
lcd_display_on();
delay_ms(100);

// 测试显示
lcd_clear(RED);
delay_ms(500);
lcd_clear(WHITE);

printf("LCD initialization complete\r\n");
```

这样可以通过串口输出来判断LCD初始化是否成功。
